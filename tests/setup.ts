// Test setup file
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Mock DBOS logger to prevent console spam during tests
jest.mock('@dbos-inc/dbos-sdk', () => ({
  DBOS: {
    logger: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    },
    transaction: () => (target: any, propertyName: string, descriptor: PropertyDescriptor) => descriptor,
    step: () => (target: any, propertyName: string, descriptor: PropertyDescriptor) => descriptor,
    workflow: () => (target: any, propertyName: string, descriptor: PropertyDescriptor) => descriptor,
    knexClient: jest.fn(),
    setConfig: jest.fn(),
    launch: jest.fn(),
    startWorkflow: jest.fn(),
  },
}));

// Mock Google Generative AI
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn(),
}));

// Mock the new services
jest.mock('../src/services/GeminiLLMService', () => ({
  GeminiLLMService: jest.fn().mockImplementation(() => ({
    generate: jest.fn(),
  })),
}));

jest.mock('../src/core/PromptService', () => ({
  PromptService: {
    getSystemPrompt: jest.fn(),
    getAvailablePrompts: jest.fn(),
    clearCache: jest.fn(),
    getCacheSize: jest.fn(),
  },
}));

jest.mock('../src/core/ConversationService', () => ({
  ConversationService: {
    createConversation: jest.fn(),
    addMessage: jest.fn(),
    getConversationMessages: jest.fn(),
    getConversation: jest.fn(),
    deleteConversation: jest.fn(),
    getRecentConversations: jest.fn(),
  },
}));

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.GEMINI_API_KEY = 'test-api-key';
process.env.DBOS_DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
