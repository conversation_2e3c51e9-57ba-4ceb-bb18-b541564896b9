import { ForaChat } from '../src/operations';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { getMockResponse } from './mocks/llmResponses';

// The services are already mocked in setup.ts

// Import the mocked services
import { ConversationService } from '../src/core/ConversationService';
import { PromptService } from '../src/core/PromptService';
import { GeminiLLMService } from '../src/services/GeminiLLMService';

const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockPromptService = PromptService as jest.Mocked<typeof PromptService>;

describe('ForaChat', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock system prompt
    mockPromptService.getSystemPrompt.mockResolvedValue('Mock system prompt content');
  });

  describe('createConversation', () => {
    it('should create a new conversation', async () => {
      const expectedConversation = {
        id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockConversationService.createConversation.mockResolvedValue(expectedConversation);

      const result = await ForaChat.createConversation();

      expect(mockConversationService.createConversation).toHaveBeenCalled();
      expect(result).toEqual(expectedConversation);
    });
  });

  describe('addMessage', () => {
    it('should add a message to a conversation', async () => {
      const expectedMessage = {
        id: 1,
        character: 'user',
        text: 'Hello world',
        conversation_id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockConversationService.addMessage.mockResolvedValue(expectedMessage);

      const result = await ForaChat.addMessage('user', 'Hello world', 1);

      expect(mockConversationService.addMessage).toHaveBeenCalledWith('user', 'Hello world', 1);
      expect(result).toEqual(expectedMessage);
    });
  });

  describe('getSystemPrompt', () => {
    it('should read system prompt from file', async () => {
      const expectedPrompt = 'System prompt content';
      mockPromptService.getSystemPrompt.mockResolvedValue(expectedPrompt);

      const result = await ForaChat.getSystemPrompt();

      expect(mockPromptService.getSystemPrompt).toHaveBeenCalled();
      expect(result).toBe(expectedPrompt);
    });
  });

  describe('chatWorkflow', () => {
    it('should complete full chat workflow for communication query', async () => {
      const userMessage = 'How can I communicate better?';
      const mockResponse = getMockResponse(userMessage);

      // Mock conversation creation
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = { id: 1, character: 'user', text: userMessage, conversation_id: 1, created_at: new Date(), updated_at: new Date() };
      const mockResponseMessage = { id: 2, character: 'Fora', text: mockResponse.reply[0].text, conversation_id: 1, created_at: new Date(), updated_at: new Date() };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      // Create a mock LLM service and inject it
      const mockLLMService = {
        generate: jest.fn().mockResolvedValue(mockResponse),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      const result = await ForaChat.chatWorkflow(userMessage);

      expect(result).toEqual(mockResponse);
      expect(result.reply).toHaveLength(4);
      expect(result.skills).toContain('clear communication');
      expect(result.theme).toBe('Effective Communication');

      // Verify workflow steps
      expect(mockConversationService.createConversation).toHaveBeenCalled();
      expect(mockConversationService.addMessage).toHaveBeenCalledTimes(2);
      expect(mockPromptService.getSystemPrompt).toHaveBeenCalled();
      expect(mockLLMService.generate).toHaveBeenCalledWith('Mock system prompt content', userMessage);
    });

    it('should handle invalid LLM response structure', async () => {
      const userMessage = 'Test message';

      // Mock conversation creation
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = { id: 1, character: 'user', text: userMessage, conversation_id: 1, created_at: new Date(), updated_at: new Date() };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage.mockResolvedValueOnce(mockUserMessage);

      // Mock invalid LLM response
      const mockLLMService = {
        generate: jest.fn().mockResolvedValue({ invalid: 'response' }),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      await expect(ForaChat.chatWorkflow(userMessage))
        .rejects.toThrow('Invalid response from LLM - missing or empty reply array');
    });

    it('should handle empty reply array', async () => {
      const userMessage = 'Test message';

      // Mock conversation creation
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = { id: 1, character: 'user', text: userMessage, conversation_id: 1, created_at: new Date(), updated_at: new Date() };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage.mockResolvedValueOnce(mockUserMessage);

      // Mock LLM response with empty reply
      const mockLLMService = {
        generate: jest.fn().mockResolvedValue({ reply: [] }),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      await expect(ForaChat.chatWorkflow(userMessage))
        .rejects.toThrow('Invalid response from LLM - missing or empty reply array');
    });

    it('should handle null LLM response', async () => {
      const userMessage = 'Test message';

      // Mock conversation creation
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = { id: 1, character: 'user', text: userMessage, conversation_id: 1, created_at: new Date(), updated_at: new Date() };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage.mockResolvedValueOnce(mockUserMessage);

      // Mock null LLM response
      const mockLLMService = {
        generate: jest.fn().mockResolvedValue(null),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      await expect(ForaChat.chatWorkflow(userMessage))
        .rejects.toThrow('Invalid response from LLM - missing or empty reply array');
    });
  });
});
