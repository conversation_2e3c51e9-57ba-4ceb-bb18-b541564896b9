import { ForaChat } from '../src/operations';
import { ConversationService } from '../src/core/ConversationService';
import { PromptService } from '../src/core/PromptService';
import { GeminiLLMService } from '../src/services/GeminiLLMService';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { getMockResponse } from './mocks/llmResponses';

// Services are already mocked in setup.ts
const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockPromptService = PromptService as jest.Mocked<typeof PromptService>;

describe('Integration Tests', () => {
  let mockConversationId = 1;
  let mockMessageId = 1;

  beforeEach(() => {
    jest.clearAllMocks();
    mockConversationId = 1;
    mockMessageId = 1;

    // Mock system prompt
    mockPromptService.getSystemPrompt.mockResolvedValue('You are a workplace skills facilitator...');
  });

  describe('End-to-End Chat Flow', () => {
    it('should handle complete communication skills conversation', async () => {
      const userMessage = 'I struggle with communicating clearly in meetings';
      const mockResponse = getMockResponse(userMessage);

      // Mock database operations
      const mockConversation = { id: mockConversationId, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = {
        id: mockMessageId++,
        character: 'user',
        text: userMessage,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };
      const mockResponseMessage = {
        id: mockMessageId++,
        character: mockResponse.reply[0].character,
        text: mockResponse.reply[0].text,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      // Mock LLM response
      const mockLLMService = {
        generate: jest.fn().mockResolvedValue(mockResponse),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      const result = await ForaChat.chatWorkflow(userMessage);

      // Verify the complete flow
      expect(result).toEqual(mockResponse);
      expect(result.reply).toHaveLength(4);
      expect(result.skills).toEqual(['clear communication', 'active listening', 'engagement']);
      expect(result.theme).toBe('Effective Communication');

      // Verify all characters are present
      const characters = result.reply.map((msg: any) => msg.character);
      expect(characters).toContain('Fora');
      expect(characters).toContain('Jan');
      expect(characters).toContain('Lou');

      // Verify service interactions
      expect(mockConversationService.createConversation).toHaveBeenCalled();
      expect(mockConversationService.addMessage).toHaveBeenCalledTimes(2);
      expect(mockPromptService.getSystemPrompt).toHaveBeenCalled();
    });

    it('should handle conflict resolution scenario', async () => {
      const userMessage = 'Two team members are constantly arguing and it affects productivity';
      const mockResponse = getMockResponse(userMessage);

      // Mock database operations
      const mockConversation = { id: mockConversationId, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = {
        id: mockMessageId++,
        character: 'user',
        text: userMessage,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };
      const mockResponseMessage = {
        id: mockMessageId++,
        character: mockResponse.reply[0].character,
        text: mockResponse.reply[0].text,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      const mockLLMService = {
        generate: jest.fn().mockResolvedValue(mockResponse),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      const result = await ForaChat.chatWorkflow(userMessage);

      expect(result.skills).toContain('conflict resolution');
      expect(result.skills).toContain('mediation');
      expect(result.theme).toBe('Workplace Conflict Resolution');

      // Verify practical advice is included
      const allText = result.reply.map((msg: any) => msg.text).join(' ');
      expect(allText.toLowerCase()).toMatch(/problem|behavior|perspective|fact/);
    });

    it('should handle leadership development query', async () => {
      const userMessage = 'I was just promoted to team lead and need guidance';
      const mockResponse = getMockResponse(userMessage);

      // Mock database operations
      const mockConversation = { id: mockConversationId, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = {
        id: mockMessageId++,
        character: 'user',
        text: userMessage,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };
      const mockResponseMessage = {
        id: mockMessageId++,
        character: mockResponse.reply[0].character,
        text: mockResponse.reply[0].text,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      const mockLLMService = {
        generate: jest.fn().mockResolvedValue(mockResponse),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      const result = await ForaChat.chatWorkflow(userMessage);

      expect(result.skills).toContain('leadership');
      expect(result.skills).toContain('team building');
      expect(result.theme).toBe('Leadership Development');

      // Verify leadership advice characteristics
      const allText = result.reply.map((msg: any) => msg.text).join(' ');
      expect(allText.toLowerCase()).toMatch(/expectation|resource|recognition|lift/);
    });

    it('should handle non-workplace queries appropriately', async () => {
      const userMessage = 'What is the weather like today?';
      const mockResponse = getMockResponse(userMessage);

      // Mock database operations
      const mockConversation = { id: mockConversationId, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = {
        id: mockMessageId++,
        character: 'user',
        text: userMessage,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };
      const mockResponseMessage = {
        id: mockMessageId++,
        character: mockResponse.reply[0].character,
        text: mockResponse.reply[0].text,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      const mockLLMService = {
        generate: jest.fn().mockResolvedValue(mockResponse),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      const result = await ForaChat.chatWorkflow(userMessage);

      expect(result.skills).toEqual([]);
      expect(result.theme).toBe('Request Clarification');
      expect(result.reply).toHaveLength(2);

      // Verify redirection message
      const allText = result.reply.map((msg: any) => msg.text).join(' ');
      expect(allText.toLowerCase()).toMatch(/workplace|professional|skill/);
    });

    it('should maintain conversation context and timing', async () => {
      const userMessage = 'Help me give better feedback to my direct reports';
      const mockResponse = getMockResponse(userMessage);

      // Mock database operations
      const mockConversation = { id: mockConversationId, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = {
        id: mockMessageId++,
        character: 'user',
        text: userMessage,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };
      const mockResponseMessage = {
        id: mockMessageId++,
        character: mockResponse.reply[0].character,
        text: mockResponse.reply[0].text,
        conversation_id: mockConversationId,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      const mockLLMService = {
        generate: jest.fn().mockResolvedValue(mockResponse),
      } as any;
      ForaChat.setLLMService(mockLLMService);

      const result = await ForaChat.chatWorkflow(userMessage);

      // Verify timing delays are present
      result.reply.forEach((message: any) => {
        expect(message.delay).toBeGreaterThan(0);
        expect(message.delay).toBeLessThanOrEqual(3000);
      });

      // Verify feedback-specific advice
      expect(result.skills).toContain('feedback');
      expect(result.theme).toBe('Giving Effective Feedback');
    });
  });
});
