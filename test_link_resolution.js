// Simple test script to verify link resolution works with actual files
const { PromptService } = require('./dist/core/PromptService');

async function testLinkResolution() {
  try {
    console.log('Testing link resolution with agent_system.md...');
    
    // Clear cache to ensure fresh read
    PromptService.clearCache();
    
    const resolvedPrompt = await PromptService.getSystemPrompt('agent_system');
    
    console.log('✅ Successfully resolved prompt');
    console.log('Length:', resolvedPrompt.length);
    
    // Check if links were resolved
    const hasCharacterDirections = resolvedPrompt.includes('### Character Directions');
    const hasSkillsList = resolvedPrompt.includes('### Interpersonal Professional Skills');
    const hasSlangList = resolvedPrompt.includes('### Gen-Z Slang');
    
    console.log('Contains Character Directions section:', hasCharacterDirections);
    console.log('Contains Skills section:', hasSkillsList);
    console.log('Contains Slang section:', hasSlangList);
    
    // Check if original links are gone
    const hasOriginalLinks = resolvedPrompt.includes('[Character Directions](character_system.md)');
    console.log('Still contains original links:', hasOriginalLinks);
    
    if (hasCharacterDirections && hasSkillsList && !hasOriginalLinks) {
      console.log('🎉 Link resolution working correctly!');
    } else {
      console.log('⚠️  Link resolution may have issues');
    }
    
  } catch (error) {
    console.error('❌ Error testing link resolution:', error.message);
  }
}

testLinkResolution();
